<launch>
  <!-- Launch file for Kinect camera with visual servoing in tuning mode -->

  <!-- Launch the Kinect camera -->
  <include file="$(find weednix_launch)/launch/kinect_camera.launch" />

  <!-- Load Kinect-specific configuration -->
  <rosparam command="load" file="$(find visual_servoing)/config/kinect_row_crop_config.yaml" ns="row_crop_follower" />

  <!-- Launch visual servoing node in tuning mode -->
  <node pkg="visual_servoing" type="row_crop_follower.py" name="row_crop_follower" output="screen" launch-prefix="python3">
    <!-- Set image topic for Kinect camera -->
    <param name="image_topic" value="/camera/rgb/image_rect_color" />

    <!-- Enable tuning mode to show all debugging windows -->
    <param name="show_frames" value="true" />
    <param name="tuning_mode" value="true" />

    <!-- Set to following_only mode to simplify tuning -->
    <param name="following_only" value="true" />
  </node>

  <!-- Launch path publisher -->
  <node name="path_publisher" pkg="visual_servoing" type="path_publisher" output="screen" />

  <!-- Optional: Add visualization in RViz -->
  <arg name="rviz" default="false"/>
  <group if="$(arg rviz)">
    <include file="$(find weednix_launch)/launch/rviz_launch.launch" />

    <!-- Load robot description for visualization -->
    <arg name="model" default="$(find robot_description)/urdf/Robot.xacro"/>
    <param name="robot_description" command="$(find xacro)/xacro $(arg model)"/>

    <!-- Add robot_state_publisher for visualization -->
    <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher">
      <param name="publish_frequency" value="50.0"/>
      <param name="ignore_timestamp" value="true"/>
    </node>
  </group>
</launch>
